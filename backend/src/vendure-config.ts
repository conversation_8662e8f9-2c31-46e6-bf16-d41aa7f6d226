import {
    dummy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    DefaultJob<PERSON>ueuePlugin,
    DefaultSearchPlugin,
    VendureConfig,
    AutoIncrementIdStrategy,
    LanguageCode,
    DefaultGuestCheckoutStrategy
} from '@vendure/core';
import { 
    defaultEmail<PERSON>and<PERSON>, 
    EmailPlugin, 
    FileBasedTemplateLoader,
    emailVerificationHandler,
    passwordResetHandler,
    emailAddressChangeHandler
} from '@vendure/email-plugin';
import { AssetServerPlugin, PresetOnlyStrategy } from '@vendure/asset-server-plugin';
import { AdminUiPlugin } from '@vendure/admin-ui-plugin';
import { GraphiqlPlugin } from '@vendure/graphiql-plugin';
import { RedisCachePlugin, DefaultSchedulerPlugin } from '@vendure/core';
import { HardenPlugin } from '@vendure/harden-plugin';
import { AuditPlugin } from './plugins/audit-plugin';
import { OrderCreationLoggerPlugin } from './plugins/order-creation-logger.plugin';
import { CustomShippingPlugin } from './plugins/custom-shipping';
import { sezzlePaymentHandler } from './sezzle-payment';
import { orderFulfillmentHandler } from './email-handlers/order-fulfillment-handler';
import { orderConfirmationHandler } from './email-handlers/order-confirmation-handler';
import 'dotenv/config';
import path from 'path';
import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { NmiPaymentPlugin } from './plugins/nmi-payment';
import { SequentialOrderCodeStrategy } from './config/sequential-order-code-strategy';
import { NewsletterPlugin } from './plugins/newsletter';
import { createSecurityMiddleware } from './middleware/security-middleware';
import { SecurityPlugin } from './plugins/security-plugin';
import { StaleOrderCleanupPlugin } from './plugins/stale-order-cleanup.plugin';
import { CacheInvalidationPlugin } from './plugins/cache-invalidation.plugin';

function validateEnvironment() {
    const required: Record<string, 'string' | 'number' | 'boolean'> = {
        'DB_NAME': 'string',
        'DB_HOST': 'string',
        'DB_PORT': 'number',
        'DB_USERNAME': 'string',
        'DB_PASSWORD': 'string',
        'SUPERADMIN_USERNAME': 'string',
        'COOKIE_SECRET': 'string',
        'GMAIL_USER': 'string',
    };

    for (const [key, type] of Object.entries(required)) {
        const value = process.env[key];
        if (!value) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
        if (type === 'number' && isNaN(Number(value))) {
            throw new Error(`Environment variable ${key} must be a number`);
        }
    }
}

validateEnvironment();

const IS_DEV = process.env.APP_ENV !== 'prod';
const serverPort = +process.env.PORT || 3000;

import { ExactStockDisplayStrategy } from './exact-stock-display-strategy';

export const config: VendureConfig = {
    entityOptions: {
        entityIdStrategy: new AutoIncrementIdStrategy(),
    },
    apiOptions: {
        port: serverPort,
        adminApiPath: 'admin-api',
        shopApiPath: 'shop-api',
        cors: {
            origin: IS_DEV 
                ? ["http://localhost:3000", "http://localhost:4000","http://localhost:8080"] 
                : ['https://damneddesigns.com/admin', "https://damneddesigns.com"],
            credentials: true,
        },
        middleware: [
            {
                handler: (req: Request, res: Response, next: NextFunction) => {
                    req.app.set('trust proxy', ['127.0.0.1', '::1', '10.0.0.0/8', '**********/12', '***********/16']);
                    next();
                },
                route: '/',
                beforeListen: true,
            },
            // Security middleware for all routes
            // {
            //     handler: createSecurityMiddleware(),
            //     route: '/',
            // },
            // ...(IS_DEV ? [] : [
            //     {
            //         handler: rateLimit({
            //             windowMs: 1 * 60 * 1000,
            //             max: 300,
            //             standardHeaders: true,
            //             legacyHeaders: false,
            //             message: 'Too many requests, please try again later.',
            //         }),
            //         route: '/shop-api',
            //     },
            //     {
            //         handler: rateLimit({
            //             windowMs: 15 * 60 * 1000,
            //             max: 10000,
            //             standardHeaders: true,
            //             legacyHeaders: false,
            //             message: 'Too many admin requests, please try again later.',
            //         }),
            //         route: '/admin-api',
            //     },
            // ]),
            // {
            //     handler: helmet({
            //         contentSecurityPolicy: {
            //             directives: {
            //                 defaultSrc: ["'self'"],
            //                 styleSrc: ["'self'", "'unsafe-inline'"],
            //                 scriptSrc: [
            //                     "'self'",
            //                     "'unsafe-eval'",  // Allow in both dev and prod for translation functionality
            //                     "https://www.google.com/recaptcha/",
            //                     "https://www.gstatic.com/recaptcha/"
            //                 ],
            //                 frameSrc: [
            //                     "'self'",
            //                     "https://www.google.com/recaptcha/",
            //                     "https://recaptcha.google.com/recaptcha/"
            //                 ],
            //                 imgSrc: ["'self'", "data:", "https:"],
            //                 connectSrc: ["'self'", "https://www.google.com/recaptcha/"],
            //                 fontSrc: ["'self'"],
            //                 objectSrc: ["'none'"],
            //                 frameAncestors: ["'none'"],
            //                 ...(IS_DEV ? { 'worker-src': ["'self' blob:"] } : {})
            //             },
            //         },
            //         hsts: {
            //             maxAge: 31536000,
            //             includeSubDomains: true,
            //             preload: true
            //         },
            //         noSniff: true,
            //         xssFilter: true,
            //         referrerPolicy: { policy: "strict-origin-when-cross-origin" }
            //     }),
            //     route: '/',
            // },
        ],
    },
    authOptions: {
        tokenMethod: ['bearer', 'cookie'],
        superadminCredentials: {
            identifier: process.env.SUPERADMIN_USERNAME,
            password: "",
        },
        cookieOptions: {
            name: '__vendure_session',
            secret: process.env.COOKIE_SECRET,
            httpOnly: true,
            secure: !IS_DEV,
            sameSite: 'strict',
            maxAge: 24 * 60 * 60 * 1000,
        },
    },
    dbConnectionOptions: {
        type: 'postgres',
        synchronize: false,
        migrations: [path.join(__dirname, './migrations/*.+(js|ts)')],
        logging: false,
        database: process.env.DB_NAME,
        schema: process.env.DB_SCHEMA,
        host: process.env.DB_HOST,
        port: +process.env.DB_PORT,
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        ssl: process.env.DB_SSL === 'true' ? {
            rejectUnauthorized: process.env.NODE_ENV === 'production',
            ca: process.env.DB_CA_CERT,
        } : false,
        extra: {
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
            statement_timeout: 30000,
            query_timeout: 30000,
        },
    },
    paymentOptions: {
        paymentMethodHandlers: [
            dummyPaymentHandler,
            sezzlePaymentHandler,
        ],
    },
    orderOptions: {
        orderCodeStrategy: new SequentialOrderCodeStrategy(),
        guestCheckoutStrategy: new DefaultGuestCheckoutStrategy({
            allowGuestCheckoutForRegisteredCustomers: true,
        }),
    },
    customFields: {},
    catalogOptions: {
        stockDisplayStrategy: new ExactStockDisplayStrategy(),
    },
    plugins: [
        OrderCreationLoggerPlugin.init(),
        StaleOrderCleanupPlugin.init(),
        CacheInvalidationPlugin.init(),
        SecurityPlugin.init({
            enableLogging: true,
            enableGraphQLProtection: true,
            minRecaptchaScore: IS_DEV ? 0.3 : 0.5
        }),
        NewsletterPlugin,
        NmiPaymentPlugin,
        CustomShippingPlugin,
        // AuditPlugin,
        HardenPlugin.init({
            maxQueryComplexity: 10000,
            apiMode: process.env.APP_ENV !== 'prod' ? 'dev' : 'prod',
            logComplexityScore: process.env.APP_ENV !== 'prod',
        }),
        // RedisCachePlugin.init({
        //     namespace: 'vendure-cache',
        //     maxItemSizeInBytes: 128_000,
        //     redisOptions: {
        //         host: process.env.REDIS_HOST || '127.0.0.1',
        //         port: parseInt(process.env.REDIS_PORT || '6379', 10),
        //         password: process.env.REDIS_PASSWORD,
        //         // Disable TLS for local Redis connections
        //         tls: process.env.REDIS_HOST !== '127.0.0.1' && process.env.NODE_ENV === 'production' ? {
        //             rejectUnauthorized: true,
        //         } : undefined,
        //         maxRetriesPerRequest: 2,
        //         connectTimeout: 3000,  // Reduced from 10000 to fail faster
        //         commandTimeout: 2000,  // Reduced from 5000 to fail faster
        //         retryStrategy: (times: number) => {
        //             const delay = Math.min(times * 100, 5000);
        //             return delay;
        //         },
        //         reconnectOnError: (err: Error) => {
        //             console.error('Redis connection error:', err.message);
        //             return true;
        //         }
        //     }
        // }),
        AssetServerPlugin.init({
            route: 'assets',
            assetUploadDir: './assets',
            assetUrlPrefix: process.env.ASSETS_URL || (IS_DEV ? undefined : 'https://damneddesigns.com/assets'),
            presets: [
                // Default Vendure presets
                { name: 'tiny', width: 50, height: 50, mode: 'crop' },
                { name: 'thumb', width: 150, height: 150, mode: 'crop' },
                { name: 'small', width: 300, height: 300, mode: 'resize' },
                { name: 'medium', width: 500, height: 500, mode: 'resize' },
                { name: 'large', width: 800, height: 800, mode: 'resize' },
                // Custom high-resolution presets for product images
                { name: 'xl', width: 1200, height: 1200, mode: 'resize' },
                { name: 'xxl', width: 1600, height: 1600, mode: 'resize' },
                { name: 'modal', width: 1600, height: 2000, mode: 'resize' },
                // Ultra high-resolution presets for large monitors
                { name: 'ultra', width: 2048, height: 2048, mode: 'resize' },
                { name: '4k', width: 2560, height: 2560, mode: 'resize' },
            ],
            imageTransformStrategy: new PresetOnlyStrategy({
                defaultPreset: 'medium',
                permittedFormats: ['jpg', 'jpeg', 'png', 'webp', 'avif'], // Enable AVIF support
            }),
        }),
        DefaultJobQueuePlugin.init({ useDatabaseForBuffer: !IS_DEV }),
        DefaultSearchPlugin.init({ bufferUpdates: false, indexStockStatus: true }),
        IS_DEV
            ? EmailPlugin.init({
                devMode: true,
                outputPath: path.join(__dirname, '../static/email/test-emails'),
                route: 'mailbox',
                handlers: [
                    orderConfirmationHandler,  // Our custom order confirmation handler
                    emailVerificationHandler,  // Default handlers excluding order confirmation
                    passwordResetHandler,
                    emailAddressChangeHandler,
                    orderFulfillmentHandler    // Our custom fulfillment handler
                ],
                templateLoader: new FileBasedTemplateLoader(path.join(__dirname, '../static/email/templates')),
                globalTemplateVars: {
                    fromAddress: `\"${process.env.STORE_NAME || 'Damned Designs'}\" <${process.env.GMAIL_USER}>`,
                    verifyEmailAddressUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/verify`,
                    passwordResetUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/password-reset`,
                    changeEmailAddressUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/verify-email-address-change`
                },
            })
            : EmailPlugin.init({
                outputPath: path.join(__dirname, '../static/email/test-emails'),
                route: 'mailbox',
                handlers: [
                    orderConfirmationHandler,  // Our custom order confirmation handler
                    emailVerificationHandler,  // Default handlers excluding order confirmation
                    passwordResetHandler,
                    emailAddressChangeHandler,
                    orderFulfillmentHandler    // Our custom fulfillment handler
                ],
                templateLoader: new FileBasedTemplateLoader(path.join(__dirname, '../static/email/templates')),
                        transport: {
                type: 'smtp',
                host: 'smtp.gmail.com',
                port: 587,
                secure: false, // true for 465, false for other ports
                auth: {
                    user: process.env.GMAIL_USER,
                    pass: process.env.EMAIL_PASS,
                },
            },
                globalTemplateVars: {
                    fromAddress: `\"${process.env.STORE_NAME || 'Damned Designs'}\" <${process.env.GMAIL_USER}>`,
                    verifyEmailAddressUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/verify`,
                    passwordResetUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/password-reset`,
                    changeEmailAddressUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/verify-email-address-change`
                },
            }),
        AdminUiPlugin.init({
            route: 'admin',
            port: serverPort,
            adminUiConfig: {
                adminApiPath: 'admin-api',
                brand: process.env.STORE_NAME || 'Damned Designs',
                hideVendureBranding: true,
                defaultLanguage: LanguageCode.en,
                availableLanguages: [LanguageCode.en],
                loginImageUrl: 'https://damneddesigns.com/assets/preview/13/damned-mascot__preview.png',
            },
        }),
        ...(IS_DEV ? [GraphiqlPlugin.init()] : []),
        DefaultSchedulerPlugin.init(),
    ],
};