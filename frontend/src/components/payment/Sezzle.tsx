import { $, Signal, component$, useSignal, useVisibleTask$ } from '@qwik.dev/core';
import type { QRL } from '@qwik.dev/core';
import { secureProcessSezzlePayment } from '~/utils/secure-api';
import { useCheckoutValidation } from '~/contexts/CheckoutValidationContext';

interface SezzleProps {
	isDisabled?: boolean;
	hideButton?: boolean;
	onForward$: QRL<(orderCode: string) => void>;
	onError$: QRL<(errorMessage: string) => void>;
	onProcessingChange$?: QRL<(isProcessing: boolean) => void>;
	triggerSignal: Signal<number>; // Incremented by parent to trigger submission
}

export default component$<SezzleProps>(({ isDisabled, hideButton = false, onForward$: _onForward$, onError$, onProcessingChange$, triggerSignal }) => {
	const isProcessing = useSignal(false);
	const error = useSignal('');
	const checkoutValidation = useCheckoutValidation();

	// For Sezzle, we don't need form validation since it's a redirect-based payment
	// But we still need to update the checkout validation context
	const updatePaymentValidationContext = $(() => {
		// Sezzle is always "valid" since there's no form to validate
		checkoutValidation.updatePaymentValidation(
			true, // Always valid
			{}, // No errors
			true // Always considered "touched" since user selected Sezzle
		);
	});

	// Update validation context when component mounts
	useVisibleTask$(() => {
		updatePaymentValidationContext();
	});

	// Core submission logic for Sezzle payment
	const submitPaymentForm = $(async () => {
		error.value = '';

		if (isProcessing.value) {
			console.log('Sezzle payment already processing, ignoring duplicate submission.');
			return;
		}

		try {
			console.log('[Sezzle] Initiating Sezzle payment...');
			isProcessing.value = true;
			
			// Notify parent about processing state change
			if (onProcessingChange$) {
				await onProcessingChange$(true);
			}

			// Process the Sezzle payment (this will create a session and return checkout URL)
			const paymentResult = await secureProcessSezzlePayment();

			console.log('[Sezzle] Payment result:', paymentResult);

			if (paymentResult?.__typename === 'Order') {
				console.log(`[Sezzle] Payment session created for order: ${paymentResult.code}`);
				
				// For Sezzle, we need to redirect to the checkout URL
				// The checkout URL should be in the payment metadata
				const payments = paymentResult.payments || [];
				console.log('[Sezzle] All payments:', payments);

				const sezzlePayment = payments.find((p: any) => p.method === 'sezzle');
				console.log('[Sezzle] Found Sezzle payment:', sezzlePayment);
				console.log('[Sezzle] Payment metadata:', sezzlePayment?.metadata);

				if (sezzlePayment?.metadata?.checkoutUrl) {
					console.log('[Sezzle] Redirecting to Sezzle checkout:', sezzlePayment.metadata.checkoutUrl);
					// Redirect to Sezzle checkout
					window.location.href = sezzlePayment.metadata.checkoutUrl;
				} else {
					console.error('[Sezzle] No checkout URL found in payment metadata');
					console.error('[Sezzle] Available metadata keys:', Object.keys(sezzlePayment?.metadata || {}));
					throw new Error('Sezzle checkout URL not found. Please try again.');
				}
			} else {
				console.error('[Sezzle] Payment failed with result:', paymentResult);
				let errorMsg = 'Sezzle payment initialization failed. Please try again.';
				if (paymentResult && typeof paymentResult === 'object' && 'errorMessage' in paymentResult) {
					errorMsg = paymentResult.errorMessage as string;
				}
				throw new Error(errorMsg);
			}
		} catch (err) {
			console.error('[Sezzle] Payment error:', err);
			error.value = err instanceof Error ? err.message : 'An unknown error occurred during Sezzle payment processing.';
			// Call the error callback with the error message
			await onError$(error.value);
		} finally {
			isProcessing.value = false;
			// Notify parent about processing state change
			if (onProcessingChange$) {
				await onProcessingChange$(false);
			}
		}
	});

	// Watch for trigger signal from parent component
	useVisibleTask$(({track}) => {
		track(() => triggerSignal.value);
		// Ensure we only trigger if the signal has a positive value (indicating a new attempt)
		// and we are not already processing a payment.
		if (triggerSignal.value > 0) {
			if (!isProcessing.value) {
				console.log('[Sezzle] Trigger signal received, initiating payment.');
				submitPaymentForm();
			} else {
				console.log('[Sezzle] Trigger signal received but payment already in progress, ignoring.');
			}
		}
	});

	return (
		<div class={`w-full ${isDisabled ? 'opacity-50 pointer-events-none' : ''} border-0`}>
			{/* Sezzle Information */}
			<div class="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6 mb-4">
				<div class="flex items-center justify-between mb-4">
					<div class="flex items-center space-x-3">
						<div class="w-12 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded flex items-center justify-center">
							<span class="text-white font-bold text-sm">S</span>
						</div>
						<div>
							<h3 class="text-lg font-semibold text-gray-900">Pay with Sezzle</h3>
							<p class="text-sm text-gray-600">Buy now, pay later in 4 interest-free installments</p>
						</div>
					</div>
				</div>
				
				<div class="space-y-2 text-sm text-gray-700">
					<div class="flex items-center space-x-2">
						<span class="w-2 h-2 bg-green-500 rounded-full"></span>
						<span>Split your purchase into 4 equal payments</span>
					</div>
					<div class="flex items-center space-x-2">
						<span class="w-2 h-2 bg-green-500 rounded-full"></span>
						<span>0% interest when you pay on time</span>
					</div>
					<div class="flex items-center space-x-2">
						<span class="w-2 h-2 bg-green-500 rounded-full"></span>
						<span>No impact to your credit score</span>
					</div>
				</div>
			</div>

			{/* Error Display */}
			{error.value && (
				<div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
								<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
							</svg>
						</div>
						<div class="ml-3">
							<p class="text-sm text-red-800">{error.value}</p>
						</div>
					</div>
				</div>
			)}

			{/* Payment Button */}
			{!hideButton && (
				<button
					type="button"
					onClick$={submitPaymentForm}
					class="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
					disabled={isProcessing.value || isDisabled}
				>
					{isProcessing.value ? (
						<>
							<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
							</svg>
							Redirecting to Sezzle...
						</>
					) : (
						<>
							<span class="mr-2">🛍️</span>
							Continue with Sezzle
						</>
					)}
				</button>
			)}

			{/* Sezzle Terms */}
			<div class="mt-4 text-xs text-gray-500 text-center">
				<p>
					By selecting Sezzle, you agree to Sezzle's{' '}
					<a href="https://sezzle.com/terms-of-use" target="_blank" rel="noopener noreferrer" class="text-purple-600 hover:text-purple-800 underline">
						Terms of Use
					</a>{' '}
					and{' '}
					<a href="https://sezzle.com/privacy-policy" target="_blank" rel="noopener noreferrer" class="text-purple-600 hover:text-purple-800 underline">
						Privacy Policy
					</a>
				</p>
			</div>
		</div>
	);
});
